using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

public class UIWorldSpaceManager : MonoBehaviour
{
    [Header("World Space Canvas Settings")]
    public Canvas worldSpaceCanvas;
    public Camera uiCamera;
    public float canvasDistance = 5f;
    
    [Header("Raycast Settings")]
    public LayerMask uiLayerMask = -1;
    public bool debugRaycast = true;
    
    private GraphicRaycaster graphicRaycaster;
    private EventSystem eventSystem;
    private PointerEventData pointerEventData;
    private List<RaycastResult> raycastResults = new List<RaycastResult>();
    
    public static UIWorldSpaceManager instance;
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
        }
        else
        {
            Destroy(gameObject);
            return;
        }
    }
    
    void Start()
    {
        SetupWorldSpaceCanvas();
        SetupEventSystem();
    }
    
    void SetupWorldSpaceCanvas()
    {
        // Jika tidak ada canvas yang di-assign, cari di scene
        if (worldSpaceCanvas == null)
        {
            worldSpaceCanvas = FindObjectOfType<Canvas>();
        }
        
        if (worldSpaceCanvas != null)
        {
            // Set canvas ke World Space mode
            worldSpaceCanvas.renderMode = RenderMode.WorldSpace;
            
            // Set camera untuk UI
            if (uiCamera == null)
            {
                uiCamera = Camera.main;
            }
            worldSpaceCanvas.worldCamera = uiCamera;
            
            // Pastikan ada GraphicRaycaster
            graphicRaycaster = worldSpaceCanvas.GetComponent<GraphicRaycaster>();
            if (graphicRaycaster == null)
            {
                graphicRaycaster = worldSpaceCanvas.gameObject.AddComponent<GraphicRaycaster>();
            }
            
            // Set posisi canvas
            RectTransform canvasRect = worldSpaceCanvas.GetComponent<RectTransform>();
            if (canvasRect != null)
            {
                Vector3 cameraForward = uiCamera.transform.forward;
                canvasRect.position = uiCamera.transform.position + cameraForward * canvasDistance;
                canvasRect.LookAt(uiCamera.transform);
                canvasRect.Rotate(0, 180, 0); // Flip agar menghadap camera
            }
            
            Debug.Log("World Space Canvas setup complete");
        }
        else
        {
            Debug.LogError("No Canvas found in scene!");
        }
    }
    
    void SetupEventSystem()
    {
        // Cari atau buat EventSystem
        eventSystem = FindObjectOfType<EventSystem>();
        if (eventSystem == null)
        {
            GameObject eventSystemGO = new GameObject("EventSystem");
            eventSystem = eventSystemGO.AddComponent<EventSystem>();
            eventSystemGO.AddComponent<StandaloneInputModule>();
            Debug.Log("EventSystem created");
        }
        
        // Setup pointer event data
        pointerEventData = new PointerEventData(eventSystem);
    }
    
    void Update()
    {
        HandleUIInput();
    }
    
    void HandleUIInput()
    {
        if (Input.GetMouseButtonDown(0))
        {
            CheckUIRaycast();
        }
    }
    
    void CheckUIRaycast()
    {
        if (graphicRaycaster == null || eventSystem == null) return;
        
        // Set posisi mouse
        pointerEventData.position = Input.mousePosition;
        
        // Clear hasil raycast sebelumnya
        raycastResults.Clear();
        
        // Lakukan raycast
        graphicRaycaster.Raycast(pointerEventData, raycastResults);
        
        if (debugRaycast)
        {
            Debug.Log($"UI Raycast results: {raycastResults.Count}");
        }
        
        // Proses hasil raycast
        foreach (RaycastResult result in raycastResults)
        {
            if (debugRaycast)
            {
                Debug.Log($"Hit UI element: {result.gameObject.name}");
            }
            
            // Cek apakah ada button component
            Button button = result.gameObject.GetComponent<Button>();
            if (button != null && button.interactable)
            {
                // Trigger button click
                button.onClick.Invoke();
                if (debugRaycast)
                {
                    Debug.Log($"Button clicked: {button.name}");
                }
                break; // Hanya klik button pertama yang ditemukan
            }
        }
    }
    
    // Method untuk mengatur jarak canvas dari camera
    public void SetCanvasDistance(float distance)
    {
        canvasDistance = distance;
        if (worldSpaceCanvas != null && uiCamera != null)
        {
            RectTransform canvasRect = worldSpaceCanvas.GetComponent<RectTransform>();
            Vector3 cameraForward = uiCamera.transform.forward;
            canvasRect.position = uiCamera.transform.position + cameraForward * canvasDistance;
        }
    }
    
    // Method untuk mengatur canvas agar selalu menghadap camera
    public void UpdateCanvasOrientation()
    {
        if (worldSpaceCanvas != null && uiCamera != null)
        {
            RectTransform canvasRect = worldSpaceCanvas.GetComponent<RectTransform>();
            canvasRect.LookAt(uiCamera.transform);
            canvasRect.Rotate(0, 180, 0);
        }
    }
    
    // Method untuk enable/disable UI interaction
    public void SetUIInteractionEnabled(bool enabled)
    {
        if (graphicRaycaster != null)
        {
            graphicRaycaster.enabled = enabled;
        }
    }
    
    void OnDrawGizmos()
    {
        if (worldSpaceCanvas != null && uiCamera != null)
        {
            // Draw line from camera to canvas
            Gizmos.color = Color.green;
            Gizmos.DrawLine(uiCamera.transform.position, worldSpaceCanvas.transform.position);
            
            // Draw canvas bounds
            RectTransform canvasRect = worldSpaceCanvas.GetComponent<RectTransform>();
            if (canvasRect != null)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireCube(canvasRect.position, canvasRect.sizeDelta);
            }
        }
    }
}
