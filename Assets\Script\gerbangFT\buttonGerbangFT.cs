using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Video;
using UnityEngine.SceneManagement;

public class VideoEndCanvas : MonoBehaviour
{
    public VideoPlayer videoPlayer;      // Drag VideoPlayer ke Inspector
    public GameObject canvasToShow;      // Drag Canvas UI ke Inspector

    void Start()
    {
        // Sembunyikan canvas di awal
        if (canvasToShow != null)   
            canvasToShow.SetActive(false);

        // Tambahkan listener untuk akhir video
        if (videoPlayer != null)
            videoPlayer.loopPointReached += OnVideoEnd;
    }

    void OnVideoEnd(VideoPlayer vp)
    {
        Debug.Log("Video selesai, munculkan canvas");

        if (canvasToShow != null)
            canvasToShow.SetActive(true);
    }

    // Fungsi ini bisa dipanggil dari button (dengan parameter nama scene)
    public void ChangeScene(string sceneName)
    {
        Debug.Log("Pindah ke scene: " + sceneName);
        SceneManager.LoadScene(sceneName);
    }
}
