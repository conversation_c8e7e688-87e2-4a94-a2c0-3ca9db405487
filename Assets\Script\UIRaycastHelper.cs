using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

public class UIRaycastHelper : MonoBehaviour
{
    [Header("Raycast Settings")]
    public Camera raycastCamera;
    public LayerMask uiLayerMask = -1;
    public bool enableDebugRaycast = true;
    public bool enableVisualDebug = false;
    
    [Header("Input Settings")]
    public bool useMouseInput = true;
    public bool useTouchInput = true;
    public KeyCode alternativeClickKey = KeyCode.Space;
    
    private EventSystem eventSystem;
    private GraphicRaycaster[] graphicRaycasters;
    private PointerEventData pointerEventData;
    private List<RaycastResult> raycastResults = new List<RaycastResult>();
    
    // Debug
    private Vector3 lastRaycastPoint;
    private bool hasValidRaycast = false;
    
    public static UIRaycastHelper instance;
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
        }
        else
        {
            Destroy(gameObject);
            return;
        }
    }
    
    void Start()
    {
        InitializeRaycastSystem();
    }
    
    void InitializeRaycastSystem()
    {
        // Setup camera
        if (raycastCamera == null)
        {
            raycastCamera = Camera.main;
        }
        
        // Setup event system
        eventSystem = EventSystem.current;
        if (eventSystem == null)
        {
            Debug.LogWarning("No EventSystem found! Creating one...");
            CreateEventSystem();
        }
        
        // Find all graphic raycasters
        RefreshGraphicRaycasters();
        
        // Setup pointer event data
        pointerEventData = new PointerEventData(eventSystem);
        
        Debug.Log("UIRaycastHelper initialized successfully");
    }
    
    void CreateEventSystem()
    {
        GameObject eventSystemGO = new GameObject("EventSystem");
        eventSystem = eventSystemGO.AddComponent<EventSystem>();
        eventSystemGO.AddComponent<StandaloneInputModule>();
    }
    
    void RefreshGraphicRaycasters()
    {
        graphicRaycasters = FindObjectsOfType<GraphicRaycaster>();
        Debug.Log($"Found {graphicRaycasters.Length} GraphicRaycasters");
    }
    
    void Update()
    {
        HandleInput();
        
        if (enableVisualDebug)
        {
            UpdateDebugRaycast();
        }
    }
    
    void HandleInput()
    {
        bool shouldRaycast = false;
        Vector2 inputPosition = Vector2.zero;
        
        // Mouse input
        if (useMouseInput)
        {
            if (Input.GetMouseButtonDown(0) || Input.GetKeyDown(alternativeClickKey))
            {
                inputPosition = Input.mousePosition;
                shouldRaycast = true;
            }
        }
        
        // Touch input
        if (useTouchInput && Input.touchCount > 0)
        {
            Touch touch = Input.GetTouch(0);
            if (touch.phase == TouchPhase.Began)
            {
                inputPosition = touch.position;
                shouldRaycast = true;
            }
        }
        
        if (shouldRaycast)
        {
            PerformUIRaycast(inputPosition);
        }
    }
    
    void UpdateDebugRaycast()
    {
        Vector2 mousePos = Input.mousePosition;
        CheckUIElementsAtPosition(mousePos);
    }
    
    public bool PerformUIRaycast(Vector2 screenPosition)
    {
        if (eventSystem == null) return false;
        
        // Setup pointer event data
        pointerEventData.position = screenPosition;
        raycastResults.Clear();
        
        // Raycast against all graphic raycasters
        bool hitSomething = false;
        
        foreach (GraphicRaycaster raycaster in graphicRaycasters)
        {
            if (raycaster != null && raycaster.enabled)
            {
                raycaster.Raycast(pointerEventData, raycastResults);
            }
        }
        
        // Process results
        if (raycastResults.Count > 0)
        {
            hitSomething = true;
            ProcessRaycastResults();
        }
        
        if (enableDebugRaycast)
        {
            Debug.Log($"UI Raycast at {screenPosition}: {raycastResults.Count} hits");
        }
        
        return hitSomething;
    }
    
    void ProcessRaycastResults()
    {
        // Sort by distance (closest first)
        raycastResults.Sort((a, b) => a.distance.CompareTo(b.distance));
        
        foreach (RaycastResult result in raycastResults)
        {
            if (enableDebugRaycast)
            {
                Debug.Log($"Hit: {result.gameObject.name} at distance {result.distance}");
            }
            
            // Try to handle different UI components
            if (HandleUIComponent(result.gameObject))
            {
                break; // Stop after first successful interaction
            }
        }
    }
    
    bool HandleUIComponent(GameObject uiObject)
    {
        // Handle Button
        Button button = uiObject.GetComponent<Button>();
        if (button != null && button.interactable)
        {
            button.onClick.Invoke();
            if (enableDebugRaycast)
            {
                Debug.Log($"Button clicked: {button.name}");
            }
            return true;
        }
        
        // Handle Toggle
        Toggle toggle = uiObject.GetComponent<Toggle>();
        if (toggle != null && toggle.interactable)
        {
            toggle.isOn = !toggle.isOn;
            if (enableDebugRaycast)
            {
                Debug.Log($"Toggle switched: {toggle.name} = {toggle.isOn}");
            }
            return true;
        }
        
        // Handle Slider
        Slider slider = uiObject.GetComponent<Slider>();
        if (slider != null && slider.interactable)
        {
            // For slider, we might want different behavior
            if (enableDebugRaycast)
            {
                Debug.Log($"Slider interacted: {slider.name}");
            }
            return true;
        }
        
        // Handle custom WorldSpaceButtonHandler
        WorldSpaceButtonHandler customButton = uiObject.GetComponent<WorldSpaceButtonHandler>();
        if (customButton != null)
        {
            customButton.OnPointerClick(pointerEventData);
            return true;
        }
        
        return false;
    }
    
    public bool CheckUIElementsAtPosition(Vector2 screenPosition)
    {
        if (eventSystem == null) return false;
        
        pointerEventData.position = screenPosition;
        raycastResults.Clear();
        
        foreach (GraphicRaycaster raycaster in graphicRaycasters)
        {
            if (raycaster != null && raycaster.enabled)
            {
                raycaster.Raycast(pointerEventData, raycastResults);
            }
        }
        
        hasValidRaycast = raycastResults.Count > 0;
        
        if (hasValidRaycast && raycastResults.Count > 0)
        {
            lastRaycastPoint = raycastResults[0].worldPosition;
        }
        
        return hasValidRaycast;
    }
    
    public bool IsPointerOverUI()
    {
        return EventSystem.current != null && EventSystem.current.IsPointerOverGameObject();
    }
    
    public bool IsPointerOverUI(int touchId)
    {
        return EventSystem.current != null && EventSystem.current.IsPointerOverGameObject(touchId);
    }
    
    // Public utility methods
    public void SetRaycastCamera(Camera camera)
    {
        raycastCamera = camera;
    }
    
    public void RefreshRaycasters()
    {
        RefreshGraphicRaycasters();
    }
    
    public void EnableDebug(bool enable)
    {
        enableDebugRaycast = enable;
        enableVisualDebug = enable;
    }
    
    // Get UI element at screen position
    public GameObject GetUIElementAtPosition(Vector2 screenPosition)
    {
        if (CheckUIElementsAtPosition(screenPosition) && raycastResults.Count > 0)
        {
            return raycastResults[0].gameObject;
        }
        return null;
    }
    
    // Get all UI elements at screen position
    public List<GameObject> GetAllUIElementsAtPosition(Vector2 screenPosition)
    {
        List<GameObject> uiElements = new List<GameObject>();
        
        if (CheckUIElementsAtPosition(screenPosition))
        {
            foreach (RaycastResult result in raycastResults)
            {
                uiElements.Add(result.gameObject);
            }
        }
        
        return uiElements;
    }
    
    void OnDrawGizmos()
    {
        if (enableVisualDebug && hasValidRaycast)
        {
            Gizmos.color = Color.red;
            Gizmos.DrawSphere(lastRaycastPoint, 0.1f);
            
            if (raycastCamera != null)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawLine(raycastCamera.transform.position, lastRaycastPoint);
            }
        }
    }
}
