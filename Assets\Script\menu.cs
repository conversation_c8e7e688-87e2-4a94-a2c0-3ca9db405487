using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;

public class menu : MonoBehaviour
{
    public GameObject MenuPanel;
    public GameObject CreditsPanel;
    public GameObject OptionsPanel;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        MenuPanel.SetActive(true);
        CreditsPanel.SetActive(false);
        OptionsPanel.SetActive(false);
    }

    // Update is called once per frame
    void Update()
    {

    }

    public void StartGame(string scenename)
    {
        SceneManager.LoadScene(scenename);
    }

    public void CreditsGame()
    {
        MenuPanel.SetActive(false);
        CreditsPanel.SetActive(true);
    }

    public void OptionsPanels()
    {
        MenuPanel.SetActive(false);
        OptionsPanel.SetActive(true);
    }

    public void BackMenuGame()
    {
        MenuPanel.SetActive(true);
        CreditsPanel.SetActive(false);
        OptionsPanel.SetActive(false);
    }

    public void QuitGame()
    {
        Debug.Log("Keluar dari game");

    #if UNITY_EDITOR
        UnityEditor.EditorApplication.isPlaying = false;
    #else
        Application.Quit();
    #endif
    }

}
