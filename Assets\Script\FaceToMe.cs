using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class FaceToMe : MonoBehaviour
{
    [Header("Face To Camera Settings")]
    public bool faceToCamera = true;
    public bool excludeUIElements = true;
    public bool onlyOnStart = true;
    public bool flipToFaceCamera = true;

    [Header("Filter Settings")]
    public string[] excludeTags = {"UI", "Button", "Canvas"};

    // Start is called before the first frame update
    void Start()
    {
        if(faceToCamera){
            FaceHotspotsToCamera();
        }
    }

    // Update is called once per frame
    void Update()
    {
        if(faceToCamera && !onlyOnStart){
            FaceHotspotsToCamera();
        }
    }

    void FaceHotspotsToCamera()
    {
        foreach(GameObject go in GameObject.FindGameObjectsWithTag("Hotspot")){

            // Skip jika objek memiliki tag yang dikecualikan
            if(excludeUIElements && ShouldExclude(go)){
                continue;
            }

            // Skip jika objek adalah UI element
            if(excludeUIElements && IsUIElement(go)){
                continue;
            }

            // Face to camera
            go.transform.LookAt(gameObject.transform.position);

            // Flip 180 degrees agar menghadap camera dengan benar
            if(flipToFaceCamera){
                go.transform.Rotate(0, 180, 0);
            }
        }
    }

    bool ShouldExclude(GameObject obj)
    {
        foreach(string excludeTag in excludeTags){
            if(obj.CompareTag(excludeTag)){
                return true;
            }
        }
        return false;
    }

    bool IsUIElement(GameObject obj)
    {
        // Cek apakah objek memiliki komponen UI
        if(obj.GetComponent<UnityEngine.UI.Button>() != null) return true;
        if(obj.GetComponent<UnityEngine.UI.Image>() != null) return true;
        if(obj.GetComponent<UnityEngine.UI.Text>() != null) return true;
        if(obj.GetComponent<Canvas>() != null) return true;

        // Cek apakah parent adalah Canvas
        Transform parent = obj.transform.parent;
        while(parent != null){
            if(parent.GetComponent<Canvas>() != null){
                return true;
            }
            parent = parent.parent;
        }

        return false;
    }

    // Method untuk mengatur face to camera secara manual
    [ContextMenu("Face All Hotspots To Camera")]
    public void FaceAllHotspotsToCamera()
    {
        FaceHotspotsToCamera();
    }

    // Method untuk mengatur satu objek menghadap camera
    public void FaceObjectToCamera(GameObject obj)
    {
        if(obj != null){
            obj.transform.LookAt(gameObject.transform.position);
            if(flipToFaceCamera){
                obj.transform.Rotate(0, 180, 0);
            }
        }
    }
}
