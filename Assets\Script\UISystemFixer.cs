using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

public class UISystemFixer : MonoBehaviour
{
    [Header("Auto Fix UI System")]
    public bool fixOnStart = true;
    
    void Start()
    {
        if(fixOnStart)
        {
            FixUISystem();
        }
    }
    
    [ContextMenu("Fix UI System")]
    public void FixUISystem()
    {
        Debug.Log("=== MEMPERBAIKI UI SYSTEM ===");
        
        // 1. Pastikan ada EventSystem
        FixEventSystem();
        
        // 2. Pastikan semua Canvas punya GraphicRaycaster
        FixCanvasRaycasters();
        
        // 3. Pastikan semua Button bisa diklik
        FixButtons();
        
        // 4. Matikan PlayerRaycast yang mengganggu
        DisablePlayerRaycast();
        
        Debug.Log("=== UI SYSTEM DIPERBAIKI ===");
    }
    
    void FixEventSystem()
    {
        EventSystem eventSystem = FindObjectOfType<EventSystem>();
        
        if(eventSystem == null)
        {
            GameObject eventSystemObj = new GameObject("EventSystem");
            eventSystem = eventSystemObj.AddComponent<EventSystem>();
            eventSystemObj.AddComponent<StandaloneInputModule>();
            
            Debug.Log("✓ EventSystem dibuat");
        }
        else
        {
            Debug.Log("✓ EventSystem sudah ada");
        }
    }
    
    void FixCanvasRaycasters()
    {
        Canvas[] canvases = FindObjectsOfType<Canvas>();
        
        foreach(Canvas canvas in canvases)
        {
            GraphicRaycaster raycaster = canvas.GetComponent<GraphicRaycaster>();
            
            if(raycaster == null)
            {
                raycaster = canvas.gameObject.AddComponent<GraphicRaycaster>();
                Debug.Log("✓ GraphicRaycaster ditambahkan ke " + canvas.name);
            }
            
            raycaster.enabled = true;
        }
        
        Debug.Log("✓ Semua Canvas memiliki GraphicRaycaster");
    }
    
    void FixButtons()
    {
        Button[] buttons = FindObjectsOfType<Button>();
        
        foreach(Button button in buttons)
        {
            // Pastikan button interactable
            button.interactable = true;
            
            // Pastikan raycast target aktif
            Graphic graphic = button.GetComponent<Graphic>();
            if(graphic != null)
            {
                graphic.raycastTarget = true;
            }
            
            Debug.Log("✓ Button " + button.name + " diperbaiki");
        }
        
        Debug.Log("✓ Semua Button diperbaiki");
    }
    
    void DisablePlayerRaycast()
    {
        PlayerRaycast playerRaycast = FindObjectOfType<PlayerRaycast>();
        
        if(playerRaycast != null)
        {
            playerRaycast.enabled = false;
            Debug.Log("✓ PlayerRaycast dimatikan untuk menghindari konflik");
        }
    }
    
    [ContextMenu("Test All Buttons")]
    public void TestAllButtons()
    {
        Button[] buttons = FindObjectsOfType<Button>();
        
        Debug.Log("=== TESTING SEMUA BUTTON ===");
        
        foreach(Button button in buttons)
        {
            Debug.Log("Testing button: " + button.name);
            Debug.Log("  - Interactable: " + button.interactable);
            Debug.Log("  - OnClick Listeners: " + button.onClick.GetPersistentEventCount());
            
            // Cek apakah ada script scene changer
            ButtonSceneLoader sceneLoader = button.GetComponent<ButtonSceneLoader>();
            SimpleSceneChanger simpleChanger = button.GetComponent<SimpleSceneChanger>();
            
            if(sceneLoader != null)
            {
                Debug.Log("  - ButtonSceneLoader: ADA");
            }
            else if(simpleChanger != null)
            {
                Debug.Log("  - SimpleSceneChanger: ADA");
            }
            else
            {
                Debug.LogWarning("  - TIDAK ADA SCRIPT SCENE CHANGER!");
            }
        }
    }
    
    [ContextMenu("Add Scene Changer To All Buttons")]
    public void AddSceneChangerToAllButtons()
    {
        Button[] buttons = FindObjectsOfType<Button>();
        
        foreach(Button button in buttons)
        {
            // Cek apakah sudah ada script scene changer
            if(button.GetComponent<ButtonSceneLoader>() == null && 
               button.GetComponent<SimpleSceneChanger>() == null)
            {
                button.gameObject.AddComponent<ButtonSceneLoader>();
                Debug.Log("✓ ButtonSceneLoader ditambahkan ke " + button.name);
            }
        }
    }
}
