using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

[System.Serializable]
public class CanvasSettings
{
    [Header("Canvas Configuration")]
    public Vector2 canvasSize = new Vector2(1920, 1080);
    public float canvasScale = 0.001f;
    public float distanceFromCamera = 5f;
    
    [Header("Sorting")]
    public int sortingOrder = 0;
    public string sortingLayerName = "Default";
    
    [Header("Interaction")]
    public bool blockingObjects = true;
    public LayerMask blockingMask = -1;
}

public class CanvasWorldSpaceSetup : MonoBehaviour
{
    [Header("Setup Settings")]
    public CanvasSettings canvasSettings = new CanvasSettings();
    public Camera targetCamera;
    public bool autoSetupOnStart = true;
    public bool createEventSystemIfMissing = true;
    
    [Header("Debug")]
    public bool showDebugInfo = true;
    public bool showGizmos = true;
    
    private Canvas canvas;
    private CanvasScaler canvasScaler;
    private GraphicRaycaster graphicRaycaster;
    private RectTransform canvasRectTransform;
    
    void Start()
    {
        if (autoSetupOnStart)
        {
            SetupWorldSpaceCanvas();
        }
    }
    
    [ContextMenu("Setup World Space Canvas")]
    public void SetupWorldSpaceCanvas()
    {
        // Get or create components
        GetOrCreateComponents();
        
        // Setup camera
        SetupCamera();
        
        // Configure canvas
        ConfigureCanvas();
        
        // Configure canvas scaler
        ConfigureCanvasScaler();
        
        // Configure graphic raycaster
        ConfigureGraphicRaycaster();
        
        // Setup event system
        SetupEventSystem();
        
        // Position canvas
        PositionCanvas();
        
        if (showDebugInfo)
        {
            Debug.Log($"World Space Canvas setup complete for: {gameObject.name}");
        }
    }
    
    void GetOrCreateComponents()
    {
        // Canvas
        canvas = GetComponent<Canvas>();
        if (canvas == null)
        {
            canvas = gameObject.AddComponent<Canvas>();
        }
        
        // Canvas Scaler
        canvasScaler = GetComponent<CanvasScaler>();
        if (canvasScaler == null)
        {
            canvasScaler = gameObject.AddComponent<CanvasScaler>();
        }
        
        // Graphic Raycaster
        graphicRaycaster = GetComponent<GraphicRaycaster>();
        if (graphicRaycaster == null)
        {
            graphicRaycaster = gameObject.AddComponent<GraphicRaycaster>();
        }
        
        // Rect Transform
        canvasRectTransform = GetComponent<RectTransform>();
    }
    
    void SetupCamera()
    {
        if (targetCamera == null)
        {
            targetCamera = Camera.main;
            if (targetCamera == null)
            {
                targetCamera = FindObjectOfType<Camera>();
            }
        }
        
        if (targetCamera == null)
        {
            Debug.LogError("No camera found! Please assign a target camera.");
        }
    }
    
    void ConfigureCanvas()
    {
        canvas.renderMode = RenderMode.WorldSpace;
        canvas.worldCamera = targetCamera;
        
        // Sorting
        canvas.sortingOrder = canvasSettings.sortingOrder;
        canvas.sortingLayerName = canvasSettings.sortingLayerName;
        
        // Additional settings
        canvas.additionalShaderChannels = AdditionalCanvasShaderChannels.Normal | 
                                         AdditionalCanvasShaderChannels.TexCoord1 | 
                                         AdditionalCanvasShaderChannels.Tangent;
    }
    
    void ConfigureCanvasScaler()
    {
        canvasScaler.uiScaleMode = CanvasScaler.ScaleMode.ConstantPixelSize;
        canvasScaler.scaleFactor = canvasSettings.canvasScale;
        canvasScaler.referencePixelsPerUnit = 100f;
    }
    
    void ConfigureGraphicRaycaster()
    {
        if (canvasSettings.blockingObjects)
        {
            graphicRaycaster.blockingObjects = GraphicRaycaster.BlockingObjects.ThreeD;
            graphicRaycaster.blockingMask = canvasSettings.blockingMask;
        }
        else
        {
            graphicRaycaster.blockingObjects = GraphicRaycaster.BlockingObjects.None;
        }
    }
    
    void SetupEventSystem()
    {
        if (createEventSystemIfMissing)
        {
            EventSystem eventSystem = FindObjectOfType<EventSystem>();
            if (eventSystem == null)
            {
                GameObject eventSystemGO = new GameObject("EventSystem");
                eventSystem = eventSystemGO.AddComponent<EventSystem>();
                eventSystemGO.AddComponent<StandaloneInputModule>();
                
                if (showDebugInfo)
                {
                    Debug.Log("EventSystem created automatically");
                }
            }
        }
    }
    
    void PositionCanvas()
    {
        if (targetCamera == null || canvasRectTransform == null) return;
        
        // Set canvas size
        canvasRectTransform.sizeDelta = canvasSettings.canvasSize;
        
        // Position canvas in front of camera
        Vector3 cameraPosition = targetCamera.transform.position;
        Vector3 cameraForward = targetCamera.transform.forward;
        
        canvasRectTransform.position = cameraPosition + cameraForward * canvasSettings.distanceFromCamera;
        
        // Make canvas face the camera
        canvasRectTransform.LookAt(targetCamera.transform);
        canvasRectTransform.Rotate(0, 180, 0); // Flip to face camera properly
    }
    
    // Public methods for runtime adjustment
    public void SetCanvasDistance(float distance)
    {
        canvasSettings.distanceFromCamera = distance;
        PositionCanvas();
    }
    
    public void SetCanvasSize(Vector2 size)
    {
        canvasSettings.canvasSize = size;
        if (canvasRectTransform != null)
        {
            canvasRectTransform.sizeDelta = size;
        }
    }
    
    public void SetCanvasScale(float scale)
    {
        canvasSettings.canvasScale = scale;
        if (canvasScaler != null)
        {
            canvasScaler.scaleFactor = scale;
        }
    }
    
    public void UpdateCanvasOrientation()
    {
        if (targetCamera != null && canvasRectTransform != null)
        {
            canvasRectTransform.LookAt(targetCamera.transform);
            canvasRectTransform.Rotate(0, 180, 0);
        }
    }
    
    public void SetSortingOrder(int order)
    {
        canvasSettings.sortingOrder = order;
        if (canvas != null)
        {
            canvas.sortingOrder = order;
        }
    }
    
    // Utility methods
    public bool IsSetupComplete()
    {
        return canvas != null && 
               canvas.renderMode == RenderMode.WorldSpace && 
               graphicRaycaster != null && 
               canvasScaler != null;
    }
    
    public void ValidateSetup()
    {
        if (!IsSetupComplete())
        {
            Debug.LogWarning("Canvas setup is incomplete. Running setup...");
            SetupWorldSpaceCanvas();
        }
    }
    
    void Update()
    {
        // Optional: Keep canvas facing camera
        if (canvas != null && canvas.renderMode == RenderMode.WorldSpace)
        {
            // Uncomment if you want canvas to always face camera
            // UpdateCanvasOrientation();
        }
    }
    
    void OnDrawGizmos()
    {
        if (!showGizmos) return;
        
        if (targetCamera != null && canvasRectTransform != null)
        {
            // Draw line from camera to canvas
            Gizmos.color = Color.green;
            Gizmos.DrawLine(targetCamera.transform.position, canvasRectTransform.position);
            
            // Draw canvas bounds
            Gizmos.color = Color.yellow;
            Vector3 size = new Vector3(canvasSettings.canvasSize.x * canvasSettings.canvasScale, 
                                     canvasSettings.canvasSize.y * canvasSettings.canvasScale, 
                                     0.1f);
            Gizmos.matrix = canvasRectTransform.localToWorldMatrix;
            Gizmos.DrawWireCube(Vector3.zero, size);
            Gizmos.matrix = Matrix4x4.identity;
            
            // Draw camera frustum intersection
            Gizmos.color = Color.red;
            Gizmos.DrawSphere(canvasRectTransform.position, 0.1f);
        }
    }
    
    void OnValidate()
    {
        // Validate settings in editor
        if (canvasSettings.canvasScale <= 0)
        {
            canvasSettings.canvasScale = 0.001f;
        }
        
        if (canvasSettings.distanceFromCamera <= 0)
        {
            canvasSettings.distanceFromCamera = 1f;
        }
    }
}
