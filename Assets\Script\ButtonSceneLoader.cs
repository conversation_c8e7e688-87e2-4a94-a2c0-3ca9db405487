using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;

public class ButtonSceneLoader : MonoBeh<PERSON>our
{
    [<PERSON><PERSON>("Nama Scene Tujuan")]
    public string sceneName = "";
    
    void Start()
    {
        // <PERSON><PERSON> komponen Button
        Button btn = GetComponent<Button>();
        
        if(btn != null)
        {
            // Tambahkan fungsi ke button
            btn.onClick.AddListener(LoadScene);
            Debug.Log("Button " + gameObject.name + " siap untuk pindah ke scene: " + sceneName);
        }
        else
        {
            Debug.LogError("Tidak ada Button component pada " + gameObject.name);
        }
    }
    
    public void LoadScene()
    {
        if(!string.IsNullOrEmpty(sceneName))
        {
            Debug.Log("Memuat scene: " + sceneName);
            SceneManager.LoadScene(sceneName);
        }
        else
        {
            Debug.LogError("Nama scene kosong! Isi field 'Scene Name' di inspector.");
        }
    }
    
    // Method ini bisa dipanggil langsung dari Button OnClick di Inspector
    public void LoadScene(string targetScene)
    {
        if(!string.IsNullOrEmpty(targetScene))
        {
            Debug.Log("Memuat scene: " + targetScene);
            SceneManager.LoadScene(targetScene);
        }
        else
        {
            Debug.LogError("Nama scene kosong!");
        }
    }
}
