using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;

public class PlayerRaycast : MonoBehaviour
{
    Ray ray;
	RaycastHit hit;
	
	public bool enabled = true;
	
	public static PlayerRaycast instance;
	
    // Start is called before the first frame update
    void Start()
    {
		instance = this;
    }

    // Update is called once per frame
    void Update()
    {
		
		if(enabled){
		
			ray = Camera.main.ScreenPointToRay(Input.mousePosition);
			if(Physics.Raycast(ray, out hit))
			{
				if(Input.GetMouseButtonDown(0)){
					
					Debug.Log(hit.collider.name);
					
					Hotspot hs = hit.collider.GetComponent<Hotspot>();
					
					if(hs.isSpawningPrefab){
						enabled = false;
						Instantiate(hs.prefabToSpawn);
					}else{
						Instantiate(Resources.Load("Loading"));
						StartCoroutine(ChangeScene(hs.targetScene));
					}
					
					
				}
					
			}
			
		}
    }
	
	IEnumerator ChangeScene(string targetScene){
		yield return new WaitForSeconds(0.1f);
		SceneManager.LoadScene(targetScene);
	}
}
