using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.EventSystems;

public class PlayerRaycast : MonoBehaviour
{
    Ray ray;
	RaycastHit hit;
	
	public bool enabled = true;
	
	public static PlayerRaycast instance;
	
    // Start is called before the first frame update
    void Start()
    {
		instance = this;
    }

    // Update is called once per frame
    void Update()
    {

		if(enabled){

			if(Input.GetMouseButtonDown(0)){

				// Cek apakah mouse di atas UI element terlebih dahulu
				if(EventSystem.current != null && EventSystem.current.IsPointerOverGameObject()){
					Debug.Log("Mouse di atas UI element, skip world raycast");
					return;
				}

				// Lakukan raycast untuk world objects
				ray = Camera.main.ScreenPointToRay(Input.mousePosition);
				if(Physics.Raycast(ray, out hit))
				{
					Debug.Log("Hit object: " + hit.collider.name);

					Hotspot hs = hit.collider.GetComponent<Hotspot>();

					if(hs != null){
						Debug.Log("Hotspot found: " + hs.name);

						if(hs.isSpawningPrefab && hs.prefabToSpawn != null){
							enabled = false;
							Instantiate(hs.prefabToSpawn);
							Debug.Log("Spawning prefab: " + hs.prefabToSpawn.name);
						}
						else if(!string.IsNullOrEmpty(hs.targetScene)){
							Instantiate(Resources.Load("Loading"));
							StartCoroutine(ChangeScene(hs.targetScene));
							Debug.Log("Changing scene to: " + hs.targetScene);
						}
					}
					else{
						Debug.Log("No Hotspot component found on: " + hit.collider.name);
					}
				}
				else{
					Debug.Log("No object hit by raycast");
				}
			}
		}
    }
	
	IEnumerator ChangeScene(string targetScene){
		yield return new WaitForSeconds(0.1f);
		SceneManager.LoadScene(targetScene);
	}
}
