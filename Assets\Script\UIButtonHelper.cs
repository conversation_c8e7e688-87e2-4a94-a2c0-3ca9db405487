using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using UnityEngine.SceneManagement;

public class UIButtonHelper : MonoBehaviour
{
    [Header("Button Setup")]
    public string targetScene = "";
    public bool debugMode = true;
    
    [Header("Auto Setup")]
    public bool autoSetupOnStart = true;
    public bool ensureEventSystem = true;
    public bool ensureGraphicRaycaster = true;
    
    private Button button;
    private Canvas parentCanvas;
    
    void Start()
    {
        if(autoSetupOnStart)
        {
            SetupButton();
            SetupEventSystem();
            SetupCanvas();
        }
    }
    
    void SetupButton()
    {
        // Get button component
        button = GetComponent<Button>();
        if(button == null)
        {
            if(debugMode) Debug.LogError("No Button component found on " + gameObject.name);
            return;
        }
        
        // Clear existing listeners
        button.onClick.RemoveAllListeners();
        
        // Add click listener
        if(!string.IsNullOrEmpty(targetScene))
        {
            button.onClick.AddListener(() => ChangeScene(targetScene));
            if(debugMode) Debug.Log("Button setup complete for scene: " + targetScene);
        }
        else
        {
            button.onClick.AddListener(() => OnButtonClick());
            if(debugMode) Debug.Log("Button setup complete with default action");
        }
        
        // Ensure button is interactable
        button.interactable = true;
        
        // Ensure raycast target is enabled
        Graphic graphic = GetComponent<Graphic>();
        if(graphic != null)
        {
            graphic.raycastTarget = true;
        }
    }
    
    void SetupEventSystem()
    {
        if(!ensureEventSystem) return;
        
        EventSystem eventSystem = FindObjectOfType<EventSystem>();
        if(eventSystem == null)
        {
            GameObject eventSystemGO = new GameObject("EventSystem");
            eventSystem = eventSystemGO.AddComponent<EventSystem>();
            eventSystemGO.AddComponent<StandaloneInputModule>();
            
            if(debugMode) Debug.Log("EventSystem created automatically");
        }
        else
        {
            if(debugMode) Debug.Log("EventSystem already exists");
        }
    }
    
    void SetupCanvas()
    {
        if(!ensureGraphicRaycaster) return;
        
        // Find parent canvas
        parentCanvas = GetComponentInParent<Canvas>();
        if(parentCanvas == null)
        {
            if(debugMode) Debug.LogError("No Canvas found in parent hierarchy of " + gameObject.name);
            return;
        }
        
        // Ensure GraphicRaycaster exists
        GraphicRaycaster raycaster = parentCanvas.GetComponent<GraphicRaycaster>();
        if(raycaster == null)
        {
            raycaster = parentCanvas.gameObject.AddComponent<GraphicRaycaster>();
            if(debugMode) Debug.Log("GraphicRaycaster added to Canvas: " + parentCanvas.name);
        }
        
        // Ensure raycaster is enabled
        raycaster.enabled = true;
        
        if(debugMode) Debug.Log("Canvas setup complete: " + parentCanvas.name);
    }
    
    public void OnButtonClick()
    {
        if(debugMode) Debug.Log("Button clicked: " + gameObject.name);
        
        if(!string.IsNullOrEmpty(targetScene))
        {
            ChangeScene(targetScene);
        }
        else
        {
            // Default action - override this method or set targetScene
            Debug.Log("Button clicked but no target scene set!");
        }
    }
    
    public void ChangeScene(string sceneName)
    {
        if(string.IsNullOrEmpty(sceneName))
        {
            if(debugMode) Debug.LogError("Scene name is empty!");
            return;
        }
        
        if(debugMode) Debug.Log("Changing scene to: " + sceneName);
        
        // Disable PlayerRaycast to avoid conflicts
        PlayerRaycast playerRaycast = FindObjectOfType<PlayerRaycast>();
        if(playerRaycast != null)
        {
            playerRaycast.enabled = false;
        }
        
        // Load loading screen if available
        GameObject loadingPrefab = Resources.Load<GameObject>("Loading");
        if(loadingPrefab != null)
        {
            Instantiate(loadingPrefab);
        }
        
        // Change scene
        StartCoroutine(ChangeSceneCoroutine(sceneName));
    }
    
    IEnumerator ChangeSceneCoroutine(string sceneName)
    {
        yield return new WaitForSeconds(0.1f);
        SceneManager.LoadScene(sceneName);
    }
    
    // Public methods for external use
    public void SetTargetScene(string sceneName)
    {
        targetScene = sceneName;
        if(debugMode) Debug.Log("Target scene set to: " + sceneName);
    }
    
    public void TestButtonClick()
    {
        if(debugMode) Debug.Log("Testing button click...");
        OnButtonClick();
    }
    
    // Method untuk mengecek apakah button setup dengan benar
    public bool IsButtonSetupCorrect()
    {
        bool isCorrect = true;
        
        // Cek button component
        if(GetComponent<Button>() == null)
        {
            if(debugMode) Debug.LogError("Missing Button component");
            isCorrect = false;
        }
        
        // Cek EventSystem
        if(FindObjectOfType<EventSystem>() == null)
        {
            if(debugMode) Debug.LogError("Missing EventSystem in scene");
            isCorrect = false;
        }
        
        // Cek Canvas dan GraphicRaycaster
        Canvas canvas = GetComponentInParent<Canvas>();
        if(canvas == null)
        {
            if(debugMode) Debug.LogError("Button not in Canvas hierarchy");
            isCorrect = false;
        }
        else if(canvas.GetComponent<GraphicRaycaster>() == null)
        {
            if(debugMode) Debug.LogError("Canvas missing GraphicRaycaster");
            isCorrect = false;
        }
        
        // Cek Raycast Target
        Graphic graphic = GetComponent<Graphic>();
        if(graphic != null && !graphic.raycastTarget)
        {
            if(debugMode) Debug.LogWarning("Graphic raycastTarget is disabled");
        }
        
        return isCorrect;
    }
    
    // Context menu untuk testing di editor
    [ContextMenu("Test Button Click")]
    public void TestButtonClickFromMenu()
    {
        TestButtonClick();
    }
    
    [ContextMenu("Check Button Setup")]
    public void CheckButtonSetupFromMenu()
    {
        bool isCorrect = IsButtonSetupCorrect();
        Debug.Log("Button setup is " + (isCorrect ? "CORRECT" : "INCORRECT"));
    }
    
    [ContextMenu("Force Setup Button")]
    public void ForceSetupFromMenu()
    {
        SetupButton();
        SetupEventSystem();
        SetupCanvas();
        Debug.Log("Button setup forced");
    }
    
    void OnValidate()
    {
        // Validate in editor
        if(Application.isPlaying && debugMode)
        {
            IsButtonSetupCorrect();
        }
    }
}
