using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using UnityEngine.SceneManagement;

public class WorldSpaceButtonHandler : <PERSON>o<PERSON>eh<PERSON>our, IPointerClickHandler, IPointerEnterHandler, IPointerExitHandler
{
    [Header("Button Settings")]
    public string targetScene = "";
    public bool isSceneChanger = true;
    public bool useCustomAction = false;
    
    [Header("Visual Feedback")]
    public bool useHoverEffect = true;
    public Color normalColor = Color.white;
    public Color hoverColor = Color.yellow;
    public Color clickColor = Color.green;
    public float colorTransitionSpeed = 5f;
    
    [Header("Audio")]
    public AudioClip hoverSound;
    public AudioClip clickSound;
    public AudioSource audioSource;
    
    [Header("Animation")]
    public bool useScaleAnimation = true;
    public Vector3 normalScale = Vector3.one;
    public Vector3 hoverScale = new Vector3(1.1f, 1.1f, 1.1f);
    public Vector3 clickScale = new Vector3(0.9f, 0.9f, 0.9f);
    public float animationSpeed = 10f;
    
    private Button button;
    private Image buttonImage;
    private Text buttonText;
    private bool isHovered = false;
    private bool isClicked = false;
    private Vector3 targetScale;
    private Color targetColor;
    
    // Events
    public System.Action OnButtonClicked;
    public System.Action OnButtonHovered;
    public System.Action OnButtonExited;
    
    void Start()
    {
        InitializeButton();
        SetupAudio();
        
        // Set initial values
        targetScale = normalScale;
        targetColor = normalColor;
        
        if (useScaleAnimation)
        {
            transform.localScale = normalScale;
        }
    }
    
    void InitializeButton()
    {
        // Get button component
        button = GetComponent<Button>();
        if (button == null)
        {
            button = gameObject.AddComponent<Button>();
        }
        
        // Get image component
        buttonImage = GetComponent<Image>();
        if (buttonImage != null && useHoverEffect)
        {
            buttonImage.color = normalColor;
        }
        
        // Get text component
        buttonText = GetComponentInChildren<Text>();
        
        // Setup button click event
        button.onClick.RemoveAllListeners();
        button.onClick.AddListener(HandleButtonClick);
        
        Debug.Log($"WorldSpaceButtonHandler initialized for: {gameObject.name}");
    }
    
    void SetupAudio()
    {
        if (audioSource == null)
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
        }
        
        // Configure audio source
        audioSource.playOnAwake = false;
        audioSource.spatialBlend = 1f; // 3D sound
    }
    
    void Update()
    {
        UpdateAnimations();
    }
    
    void UpdateAnimations()
    {
        // Update scale animation
        if (useScaleAnimation)
        {
            transform.localScale = Vector3.Lerp(transform.localScale, targetScale, Time.deltaTime * animationSpeed);
        }
        
        // Update color animation
        if (useHoverEffect && buttonImage != null)
        {
            buttonImage.color = Color.Lerp(buttonImage.color, targetColor, Time.deltaTime * colorTransitionSpeed);
        }
    }
    
    public void OnPointerClick(PointerEventData eventData)
    {
        Debug.Log($"WorldSpace Button clicked: {gameObject.name}");
        
        // Visual feedback
        StartCoroutine(ClickAnimation());
        
        // Audio feedback
        PlaySound(clickSound);
        
        // Trigger custom event
        OnButtonClicked?.Invoke();
        
        // Handle button action
        HandleButtonClick();
    }
    
    public void OnPointerEnter(PointerEventData eventData)
    {
        Debug.Log($"WorldSpace Button hovered: {gameObject.name}");
        
        isHovered = true;
        
        // Visual feedback
        if (useHoverEffect)
        {
            targetColor = hoverColor;
        }
        
        if (useScaleAnimation)
        {
            targetScale = hoverScale;
        }
        
        // Audio feedback
        PlaySound(hoverSound);
        
        // Trigger custom event
        OnButtonHovered?.Invoke();
    }
    
    public void OnPointerExit(PointerEventData eventData)
    {
        Debug.Log($"WorldSpace Button exit: {gameObject.name}");
        
        isHovered = false;
        
        // Visual feedback
        if (useHoverEffect)
        {
            targetColor = normalColor;
        }
        
        if (useScaleAnimation)
        {
            targetScale = normalScale;
        }
        
        // Trigger custom event
        OnButtonExited?.Invoke();
    }
    
    void HandleButtonClick()
    {
        if (useCustomAction)
        {
            // Custom action - override this method or use events
            Debug.Log($"Custom action for button: {gameObject.name}");
        }
        else if (isSceneChanger && !string.IsNullOrEmpty(targetScene))
        {
            // Scene change action
            Debug.Log($"Changing scene to: {targetScene}");
            ChangeScene(targetScene);
        }
    }
    
    void ChangeScene(string sceneName)
    {
        // Add loading effect if available
        GameObject loadingPrefab = Resources.Load<GameObject>("Loading");
        if (loadingPrefab != null)
        {
            Instantiate(loadingPrefab);
        }
        
        // Change scene
        StartCoroutine(ChangeSceneCoroutine(sceneName));
    }
    
    IEnumerator ChangeSceneCoroutine(string sceneName)
    {
        yield return new WaitForSeconds(0.1f);
        SceneManager.LoadScene(sceneName);
    }
    
    IEnumerator ClickAnimation()
    {
        if (useScaleAnimation)
        {
            // Scale down
            targetScale = clickScale;
            yield return new WaitForSeconds(0.1f);
            
            // Scale back to hover or normal
            targetScale = isHovered ? hoverScale : normalScale;
        }
        
        if (useHoverEffect && buttonImage != null)
        {
            // Flash click color
            targetColor = clickColor;
            yield return new WaitForSeconds(0.1f);
            
            // Back to hover or normal color
            targetColor = isHovered ? hoverColor : normalColor;
        }
    }
    
    void PlaySound(AudioClip clip)
    {
        if (audioSource != null && clip != null)
        {
            audioSource.clip = clip;
            audioSource.Play();
        }
    }
    
    // Public methods for external control
    public void SetTargetScene(string sceneName)
    {
        targetScene = sceneName;
        isSceneChanger = true;
        useCustomAction = false;
    }
    
    public void SetCustomAction(System.Action action)
    {
        OnButtonClicked = action;
        useCustomAction = true;
        isSceneChanger = false;
    }
    
    public void SetInteractable(bool interactable)
    {
        if (button != null)
        {
            button.interactable = interactable;
        }
    }
    
    public void SetButtonText(string text)
    {
        if (buttonText != null)
        {
            buttonText.text = text;
        }
    }
    
    // Method untuk testing dari inspector
    [ContextMenu("Test Button Click")]
    public void TestButtonClick()
    {
        OnPointerClick(null);
    }
}
