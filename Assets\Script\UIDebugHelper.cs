using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

public class UIDebugHelper : MonoBehaviour
{
    [Header("Debug Settings")]
    public bool enableDebugLogs = true;
    public bool showUIInfo = true;
    public bool trackMousePosition = true;
    public bool showRaycastResults = true;
    
    [Header("Visual Debug")]
    public bool showDebugGUI = true;
    public bool showCanvasInfo = true;
    
    private EventSystem eventSystem;
    private List<RaycastResult> raycastResults = new List<RaycastResult>();
    private PointerEventData pointerEventData;
    private string debugInfo = "";
    
    void Start()
    {
        eventSystem = EventSystem.current;
        if(eventSystem != null)
        {
            pointerEventData = new PointerEventData(eventSystem);
        }
        
        if(enableDebugLogs)
        {
            LogUISystemInfo();
        }
    }
    
    void Update()
    {
        if(trackMousePosition && eventSystem != null)
        {
            UpdateMouseTracking();
        }
        
        if(showUIInfo)
        {
            UpdateDebugInfo();
        }
    }
    
    void UpdateMouseTracking()
    {
        pointerEventData.position = Input.mousePosition;
        raycastResults.Clear();
        
        eventSystem.RaycastAll(pointerEventData, raycastResults);
        
        if(showRaycastResults && raycastResults.Count > 0)
        {
            foreach(RaycastResult result in raycastResults)
            {
                if(enableDebugLogs)
                {
                    Debug.Log($"UI Raycast hit: {result.gameObject.name} at distance {result.distance}");
                }
            }
        }
    }
    
    void UpdateDebugInfo()
    {
        debugInfo = "=== UI DEBUG INFO ===\n";
        debugInfo += $"Mouse Position: {Input.mousePosition}\n";
        debugInfo += $"EventSystem: {(eventSystem != null ? "OK" : "MISSING")}\n";
        debugInfo += $"Pointer Over UI: {(eventSystem != null ? eventSystem.IsPointerOverGameObject() : false)}\n";
        debugInfo += $"UI Raycast Hits: {raycastResults.Count}\n";
        
        if(raycastResults.Count > 0)
        {
            debugInfo += "Hit Objects:\n";
            foreach(RaycastResult result in raycastResults)
            {
                debugInfo += $"  - {result.gameObject.name}\n";
            }
        }
        
        // Canvas info
        Canvas[] canvases = FindObjectsOfType<Canvas>();
        debugInfo += $"\nCanvases in scene: {canvases.Length}\n";
        foreach(Canvas canvas in canvases)
        {
            debugInfo += $"  - {canvas.name} ({canvas.renderMode})\n";
            GraphicRaycaster raycaster = canvas.GetComponent<GraphicRaycaster>();
            debugInfo += $"    GraphicRaycaster: {(raycaster != null ? "OK" : "MISSING")}\n";
        }
        
        // Button info
        Button[] buttons = FindObjectsOfType<Button>();
        debugInfo += $"\nButtons in scene: {buttons.Length}\n";
        foreach(Button button in buttons)
        {
            debugInfo += $"  - {button.name} (Interactable: {button.interactable})\n";
        }
    }
    
    void LogUISystemInfo()
    {
        Debug.Log("=== UI SYSTEM DIAGNOSTIC ===");
        
        // EventSystem check
        EventSystem[] eventSystems = FindObjectsOfType<EventSystem>();
        Debug.Log($"EventSystems found: {eventSystems.Length}");
        if(eventSystems.Length == 0)
        {
            Debug.LogError("NO EVENTSYSTEM FOUND! UI will not work.");
        }
        else if(eventSystems.Length > 1)
        {
            Debug.LogWarning("Multiple EventSystems found! This may cause issues.");
        }
        
        // Canvas check
        Canvas[] canvases = FindObjectsOfType<Canvas>();
        Debug.Log($"Canvases found: {canvases.Length}");
        foreach(Canvas canvas in canvases)
        {
            Debug.Log($"Canvas: {canvas.name}");
            Debug.Log($"  Render Mode: {canvas.renderMode}");
            Debug.Log($"  Sorting Order: {canvas.sortingOrder}");
            
            GraphicRaycaster raycaster = canvas.GetComponent<GraphicRaycaster>();
            if(raycaster == null)
            {
                Debug.LogError($"  MISSING GraphicRaycaster on {canvas.name}!");
            }
            else
            {
                Debug.Log($"  GraphicRaycaster: OK (Enabled: {raycaster.enabled})");
            }
        }
        
        // Button check
        Button[] buttons = FindObjectsOfType<Button>();
        Debug.Log($"Buttons found: {buttons.Length}");
        foreach(Button button in buttons)
        {
            Debug.Log($"Button: {button.name}");
            Debug.Log($"  Interactable: {button.interactable}");
            Debug.Log($"  OnClick Listeners: {button.onClick.GetPersistentEventCount()}");
            
            Graphic graphic = button.GetComponent<Graphic>();
            if(graphic != null)
            {
                Debug.Log($"  Raycast Target: {graphic.raycastTarget}");
            }
        }
        
        // PlayerRaycast check
        PlayerRaycast playerRaycast = FindObjectOfType<PlayerRaycast>();
        if(playerRaycast != null)
        {
            Debug.Log($"PlayerRaycast found: {playerRaycast.name} (Enabled: {playerRaycast.enabled})");
        }
        
        Debug.Log("=== END DIAGNOSTIC ===");
    }
    
    void OnGUI()
    {
        if(!showDebugGUI) return;
        
        GUI.Box(new Rect(10, 10, 300, 400), debugInfo);
        
        if(GUI.Button(new Rect(10, 420, 100, 30), "Refresh Info"))
        {
            LogUISystemInfo();
        }
        
        if(GUI.Button(new Rect(120, 420, 100, 30), "Test UI Click"))
        {
            TestUIClick();
        }
        
        if(GUI.Button(new Rect(230, 420, 80, 30), "Fix UI"))
        {
            FixUISystem();
        }
    }
    
    public void TestUIClick()
    {
        Vector2 screenCenter = new Vector2(Screen.width / 2f, Screen.height / 2f);
        
        if(eventSystem != null)
        {
            pointerEventData.position = screenCenter;
            raycastResults.Clear();
            eventSystem.RaycastAll(pointerEventData, raycastResults);
            
            Debug.Log($"Test UI Click at screen center: {raycastResults.Count} hits");
            
            foreach(RaycastResult result in raycastResults)
            {
                Debug.Log($"Hit: {result.gameObject.name}");
                
                Button button = result.gameObject.GetComponent<Button>();
                if(button != null && button.interactable)
                {
                    Debug.Log($"Triggering button: {button.name}");
                    button.onClick.Invoke();
                    break;
                }
            }
        }
    }
    
    public void FixUISystem()
    {
        Debug.Log("Attempting to fix UI system...");
        
        // Ensure EventSystem exists
        if(FindObjectOfType<EventSystem>() == null)
        {
            GameObject eventSystemGO = new GameObject("EventSystem");
            eventSystemGO.AddComponent<EventSystem>();
            eventSystemGO.AddComponent<StandaloneInputModule>();
            Debug.Log("Created EventSystem");
        }
        
        // Fix Canvas GraphicRaycasters
        Canvas[] canvases = FindObjectsOfType<Canvas>();
        foreach(Canvas canvas in canvases)
        {
            if(canvas.GetComponent<GraphicRaycaster>() == null)
            {
                canvas.gameObject.AddComponent<GraphicRaycaster>();
                Debug.Log($"Added GraphicRaycaster to {canvas.name}");
            }
        }
        
        // Fix Button raycast targets
        Button[] buttons = FindObjectsOfType<Button>();
        foreach(Button button in buttons)
        {
            Graphic graphic = button.GetComponent<Graphic>();
            if(graphic != null && !graphic.raycastTarget)
            {
                graphic.raycastTarget = true;
                Debug.Log($"Enabled raycast target for {button.name}");
            }
        }
        
        Debug.Log("UI system fix complete");
        LogUISystemInfo();
    }
    
    // Context menu methods
    [ContextMenu("Log UI System Info")]
    public void LogUISystemInfoFromMenu()
    {
        LogUISystemInfo();
    }
    
    [ContextMenu("Test UI Click")]
    public void TestUIClickFromMenu()
    {
        TestUIClick();
    }
    
    [ContextMenu("Fix UI System")]
    public void FixUISystemFromMenu()
    {
        FixUISystem();
    }
}
