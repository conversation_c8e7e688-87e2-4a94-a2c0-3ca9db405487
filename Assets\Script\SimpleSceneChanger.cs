using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using UnityEngine.EventSystems;

public class SimpleSceneChanger : MonoBehaviour
{
    [Header("Scene Settings")]
    public string targetSceneName = "";
    
    [Header("Auto Setup")]
    public bool autoSetup = true;
    
    private Button button;
    
    void Start()
    {
        if(autoSetup)
        {
            SetupButton();
            EnsureEventSystem();
            EnsureCanvasRaycaster();
        }
    }
    
    void SetupButton()
    {
        // Ambil komponen Button
        button = GetComponent<Button>();
        
        if(button == null)
        {
            Debug.LogError("Tidak ada komponen Button pada " + gameObject.name);
            return;
        }
        
        // Hapus listener lama
        button.onClick.RemoveAllListeners();
        
        // Tambah listener baru
        button.onClick.AddListener(ChangeScene);
        
        // Pastikan button bisa di-interact
        button.interactable = true;
        
        Debug.Log("Button setup selesai untuk scene: " + targetSceneName);
    }
    
    void EnsureEventSystem()
    {
        // Cek apakah ada EventSystem
        EventSystem eventSystem = FindObjectOfType<EventSystem>();
        
        if(eventSystem == null)
        {
            // Buat EventSystem baru
            GameObject eventSystemObj = new GameObject("EventSystem");
            eventSystem = eventSystemObj.AddComponent<EventSystem>();
            eventSystemObj.AddComponent<StandaloneInputModule>();
            
            Debug.Log("EventSystem dibuat otomatis");
        }
    }
    
    void EnsureCanvasRaycaster()
    {
        // Cari Canvas parent
        Canvas canvas = GetComponentInParent<Canvas>();
        
        if(canvas == null)
        {
            Debug.LogError("Button tidak berada dalam Canvas!");
            return;
        }
        
        // Pastikan ada GraphicRaycaster
        GraphicRaycaster raycaster = canvas.GetComponent<GraphicRaycaster>();
        
        if(raycaster == null)
        {
            raycaster = canvas.gameObject.AddComponent<GraphicRaycaster>();
            Debug.Log("GraphicRaycaster ditambahkan ke Canvas");
        }
        
        // Pastikan raycaster aktif
        raycaster.enabled = true;
    }
    
    public void ChangeScene()
    {
        if(string.IsNullOrEmpty(targetSceneName))
        {
            Debug.LogError("Nama scene target kosong!");
            return;
        }
        
        Debug.Log("Pindah ke scene: " + targetSceneName);
        
        // Matikan PlayerRaycast jika ada
        PlayerRaycast playerRaycast = FindObjectOfType<PlayerRaycast>();
        if(playerRaycast != null)
        {
            playerRaycast.enabled = false;
        }
        
        // Pindah scene
        SceneManager.LoadScene(targetSceneName);
    }
    
    // Method untuk set scene dari luar
    public void SetTargetScene(string sceneName)
    {
        targetSceneName = sceneName;
    }
    
    // Method untuk test button
    public void TestButton()
    {
        Debug.Log("Test button: " + gameObject.name);
        ChangeScene();
    }
}
