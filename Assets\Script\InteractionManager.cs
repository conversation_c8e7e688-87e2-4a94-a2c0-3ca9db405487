using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class InteractionManager : MonoBehaviour
{
    [Header("Interaction Settings")]
    public bool enableUIInteraction = true;
    public bool enableWorldInteraction = true;
    public bool uiBlocksWorldInteraction = true;
    
    [Header("Input Settings")]
    public KeyCode interactionKey = KeyCode.Mouse0;
    public bool useMousePosition = true;
    public bool useTouchInput = true;
    
    [Header("Debug")]
    public bool showDebugLogs = true;
    public bool showDebugRays = false;
    
    private Camera mainCamera;
    private EventSystem eventSystem;
    private PlayerRaycast playerRaycast;
    private UIRaycastHelper uiRaycastHelper;
    
    public static InteractionManager instance;
    
    // Events
    public System.Action<GameObject> OnUIElementClicked;
    public System.Action<GameObject> OnWorldObjectClicked;
    public System.Action<Vector2> OnEmptySpaceClicked;
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
        }
        else
        {
            Destroy(gameObject);
            return;
        }
    }
    
    void Start()
    {
        InitializeComponents();
    }
    
    void InitializeComponents()
    {
        // Get main camera
        mainCamera = Camera.main;
        if (mainCamera == null)
        {
            mainCamera = FindObjectOfType<Camera>();
        }
        
        // Get event system
        eventSystem = EventSystem.current;
        
        // Get player raycast
        playerRaycast = FindObjectOfType<PlayerRaycast>();
        
        // Get UI raycast helper
        uiRaycastHelper = FindObjectOfType<UIRaycastHelper>();
        
        if (showDebugLogs)
        {
            Debug.Log("InteractionManager initialized");
        }
    }
    
    void Update()
    {
        HandleInput();
    }
    
    void HandleInput()
    {
        bool inputDetected = false;
        Vector2 inputPosition = Vector2.zero;
        
        // Mouse input
        if (useMousePosition && Input.GetKeyDown(interactionKey))
        {
            inputPosition = Input.mousePosition;
            inputDetected = true;
        }
        
        // Touch input
        if (useTouchInput && Input.touchCount > 0)
        {
            Touch touch = Input.GetTouch(0);
            if (touch.phase == TouchPhase.Began)
            {
                inputPosition = touch.position;
                inputDetected = true;
            }
        }
        
        if (inputDetected)
        {
            ProcessInteraction(inputPosition);
        }
    }
    
    void ProcessInteraction(Vector2 screenPosition)
    {
        bool uiHit = false;
        bool worldHit = false;
        
        // Check UI interaction first
        if (enableUIInteraction)
        {
            uiHit = CheckUIInteraction(screenPosition);
        }
        
        // Check world interaction if UI didn't block it
        if (enableWorldInteraction && (!uiBlocksWorldInteraction || !uiHit))
        {
            worldHit = CheckWorldInteraction(screenPosition);
        }
        
        // If nothing was hit, trigger empty space click
        if (!uiHit && !worldHit)
        {
            OnEmptySpaceClicked?.Invoke(screenPosition);
            if (showDebugLogs)
            {
                Debug.Log($"Empty space clicked at: {screenPosition}");
            }
        }
    }
    
    bool CheckUIInteraction(Vector2 screenPosition)
    {
        // Method 1: Use EventSystem
        if (eventSystem != null && eventSystem.IsPointerOverGameObject())
        {
            if (showDebugLogs)
            {
                Debug.Log("UI element detected by EventSystem");
            }
            return true;
        }
        
        // Method 2: Use UIRaycastHelper if available
        if (uiRaycastHelper != null)
        {
            bool hit = uiRaycastHelper.PerformUIRaycast(screenPosition);
            if (hit)
            {
                GameObject uiElement = uiRaycastHelper.GetUIElementAtPosition(screenPosition);
                if (uiElement != null)
                {
                    OnUIElementClicked?.Invoke(uiElement);
                    if (showDebugLogs)
                    {
                        Debug.Log($"UI element clicked: {uiElement.name}");
                    }
                }
                return true;
            }
        }
        
        // Method 3: Manual UI raycast
        return PerformManualUIRaycast(screenPosition);
    }
    
    bool PerformManualUIRaycast(Vector2 screenPosition)
    {
        // Find all graphic raycasters
        GraphicRaycaster[] raycasters = FindObjectsOfType<GraphicRaycaster>();
        
        if (raycasters.Length == 0) return false;
        
        PointerEventData pointerData = new PointerEventData(eventSystem)
        {
            position = screenPosition
        };
        
        List<RaycastResult> results = new List<RaycastResult>();
        
        foreach (GraphicRaycaster raycaster in raycasters)
        {
            if (raycaster.enabled)
            {
                raycaster.Raycast(pointerData, results);
            }
        }
        
        if (results.Count > 0)
        {
            // Sort by distance
            results.Sort((a, b) => a.distance.CompareTo(b.distance));
            
            GameObject hitObject = results[0].gameObject;
            OnUIElementClicked?.Invoke(hitObject);
            
            if (showDebugLogs)
            {
                Debug.Log($"Manual UI raycast hit: {hitObject.name}");
            }
            
            return true;
        }
        
        return false;
    }
    
    bool CheckWorldInteraction(Vector2 screenPosition)
    {
        if (mainCamera == null) return false;
        
        Ray ray = mainCamera.ScreenPointToRay(screenPosition);
        RaycastHit hit;
        
        if (showDebugRays)
        {
            Debug.DrawRay(ray.origin, ray.direction * 100f, Color.red, 1f);
        }
        
        if (Physics.Raycast(ray, out hit))
        {
            GameObject hitObject = hit.collider.gameObject;
            OnWorldObjectClicked?.Invoke(hitObject);
            
            if (showDebugLogs)
            {
                Debug.Log($"World object clicked: {hitObject.name}");
            }
            
            // Check if it's a hotspot
            Hotspot hotspot = hitObject.GetComponent<Hotspot>();
            if (hotspot != null)
            {
                HandleHotspotInteraction(hotspot);
            }
            
            return true;
        }
        
        return false;
    }
    
    void HandleHotspotInteraction(Hotspot hotspot)
    {
        if (playerRaycast != null)
        {
            // Disable player raycast temporarily to avoid conflicts
            playerRaycast.enabled = false;
            
            if (hotspot.isSpawningPrefab && hotspot.prefabToSpawn != null)
            {
                Instantiate(hotspot.prefabToSpawn);
            }
            else if (!string.IsNullOrEmpty(hotspot.targetScene))
            {
                // Load scene
                GameObject loadingPrefab = Resources.Load<GameObject>("Loading");
                if (loadingPrefab != null)
                {
                    Instantiate(loadingPrefab);
                }
                
                StartCoroutine(ChangeSceneCoroutine(hotspot.targetScene));
            }
        }
    }
    
    IEnumerator ChangeSceneCoroutine(string sceneName)
    {
        yield return new WaitForSeconds(0.1f);
        UnityEngine.SceneManagement.SceneManager.LoadScene(sceneName);
    }
    
    // Public methods for external control
    public void SetUIInteractionEnabled(bool enabled)
    {
        enableUIInteraction = enabled;
    }
    
    public void SetWorldInteractionEnabled(bool enabled)
    {
        enableWorldInteraction = enabled;
    }
    
    public void SetUIBlocksWorld(bool blocks)
    {
        uiBlocksWorldInteraction = blocks;
    }
    
    public void EnablePlayerRaycast(bool enabled)
    {
        if (playerRaycast != null)
        {
            playerRaycast.enabled = enabled;
        }
    }
    
    public bool IsPointerOverUI()
    {
        return eventSystem != null && eventSystem.IsPointerOverGameObject();
    }
    
    public bool IsPointerOverUI(int touchId)
    {
        return eventSystem != null && eventSystem.IsPointerOverGameObject(touchId);
    }
    
    // Debug methods
    [ContextMenu("Test UI Interaction")]
    public void TestUIInteraction()
    {
        Vector2 screenCenter = new Vector2(Screen.width / 2f, Screen.height / 2f);
        CheckUIInteraction(screenCenter);
    }
    
    [ContextMenu("Test World Interaction")]
    public void TestWorldInteraction()
    {
        Vector2 screenCenter = new Vector2(Screen.width / 2f, Screen.height / 2f);
        CheckWorldInteraction(screenCenter);
    }
}
